# Firebase Setup Instructions

## Overview
The free AI courses page has been updated to fetch course data dynamically from Firebase Firestore. The system shows error messages if courses cannot be loaded.

## Current Status
✅ Firebase Firestore integration code is implemented
✅ Firebase configuration is set up
✅ Course click functionality with localStorage implemented

## How to Configure Firebase

### Step 1: Get Firebase Configuration
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `hashinclude2024`
3. Click on the gear icon (Settings) → Project settings
4. Scroll down to "Your apps" section
5. If you don't have a web app, click "Add app" and select the web icon
6. Copy the configuration object

### Step 2: Update Configuration
1. Open `public/script/firebase-config.js`
2. Replace the placeholder values with your actual Firebase configuration:

```javascript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "hashinclude2024.firebaseapp.com",
  databaseURL: "https://hashinclude2024-default-rtdb.firebaseio.com",
  projectId: "hashinclude2024",
  storageBucket: "hashinclude2024.appspot.com",
  messagingSenderId: "your-actual-sender-id",
  appId: "your-actual-app-id"
};
```

### Step 3: Set Up Firestore Database Structure
Create the following structure in your Firebase Firestore:

**Path:** `/toolData/courses/list`

**Structure:**
- Collection: `toolData`
- Document: `courses`
- Subcollection: `list`
- Documents in `list` subcollection: Individual course documents

**Example course documents in the `list` subcollection:**

**Document ID:** `course1`
```json
{
  "title": "Introduction to AI and Generative AI",
  "description": "Learn the fundamentals of AI/UX design with hands-on projects and industry-standard tools.",
  "enrollments": 1000
}
```

**Document ID:** `course2`
```json
{
  "title": "AI Tools (ChatGPT, Gemini, Claude)",
  "description": "Explore popular AI platforms and their applications.",
  "enrollments": 2000
}
```

### Step 4: Firestore Security Rules
Make sure your Firestore security rules allow read access to the subcollection:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /toolData/courses/list/{courseId} {
      allow read: if true;
      allow write: if false;
    }
  }
}
```

## Course Data Structure
Each course should have the following properties:
- `title` (string): The course title
- `description` (string): Course description
- `enrollments` (number): Number of students enrolled

## Fallback Behavior
If Firebase is not configured or fails to load:
- The system will automatically use fallback course data
- No error will be shown to users
- The page will function normally with sample courses

## Testing
1. Open `free-ai-courses.html` in a browser
2. Check the browser console for any Firebase-related messages
3. Verify that courses are loading (either from Firebase or fallback)

## Troubleshooting
- Check browser console for error messages
- Verify Firebase configuration values are correct
- Ensure database rules allow read access
- Test with fallback data first, then configure Firebase

## Files Modified
- `public/free-ai-courses.html` - Updated with dynamic course loading
- `public/script/firebase-config.js` - Firebase configuration file (new)
- `FIREBASE_SETUP.md` - This instruction file (new)
