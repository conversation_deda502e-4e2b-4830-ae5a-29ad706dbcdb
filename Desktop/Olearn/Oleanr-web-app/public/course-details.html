<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />

  <title>Course Details - Introduction to AI and Generative AI | Olearn.ai</title>
  <meta name="description"
    content="Get on the fast track to a career in UX design. In this certificate program, you'll learn in-demand skills and get AI training from Google experts.">
  <meta name="keywords"
    content="AI course, generative AI, Google certification, UX design, AI training, course details">
  <meta name="author" content="Olearn.ai">
  <meta name="robots" content="index, follow">

  <!-- Open Graph (OG) Meta Tags for Social Media -->
  <meta property="og:title" content="Course Details - Introduction to AI and Generative AI">
  <meta property="og:description"
    content="Get on the fast track to a career in UX design. Learn in-demand skills with AI training from Google experts.">
  <meta property="og:image" content="https://olearn.ai/assets/olearn-logo.svg">
  <meta property="og:url" content="https://olearn.ai/course-details.html">
  <meta property="og:type" content="website">

  <!-- Canonical URL -->
  <link rel="canonical" href="https://olearn.ai/course-details.html">

  <link rel="shortcut icon" href="assets/olearn-logo.svg" type="image/x-icon">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
    integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link rel="stylesheet" href="style/style.css" />

  <style>
    body{
      font-family: 'DM Sans', sans-serif !important;
      font-weight: 200;
    }
    .course-hero-section {
      padding: 60px 0 80px 0;
      background: var(--bg-primary);
    }

    .google-logo {
      width: 120px;
      height: auto;
      margin-bottom: 40px;
    }

    .course-main-title {
      color: var(--heading-color);
      margin-bottom: 20px;
      line-height: 1.2;
      font-style: Regular;
      font-size: 48px;
      line-height: 26px;
      letter-spacing: 0%;
    }

    .course-description {
      color: var(--content-color);
      font-size: 1.1rem;
      line-height: 1.6;
      margin-bottom: 30px;
      max-width: 100%;
    }

    .enroll-btn {
      background: #2E71E5;
      color: white;
      border: none;
      border-radius: 8px;
      padding: 12px 32px;
      /* font-weight: 600; */
      font-size: 1rem;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
      margin-bottom: 20px;
    }

    .enroll-btn:hover {
      background: #1e5bb8;
      color: white;
      transform: translateY(-2px);
    }

    .enrollment-count {
      color: var(--content-color);
      font-size: 0.9rem;
      margin-bottom: 40px;
    }

    .enrollment-count-digit{
      color: #FFFF;
    }

    .course-stats {
      background: rgba(79, 86, 120, 0.1);
      border: 1px solid #3C4561;
      border-radius: 16px;
      padding: 20px 150px;
      margin-bottom: 40px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 32px;
    }

    .stat-item {
      /* text-align: center; */
    }

    .stat-number {
      color: var(--heading-color);
      font-size: 20px;
      /* font-weight: 700; */
      display: block;
      margin-bottom: 8px;
    }

    .stat-label {
      color: var(--heading-color);
      font-size: 1rem;
      /* font-weight: 600; */
      margin-bottom: 8px;
    }

    .stat-description {
      color: var(--content-color);
      font-size: 0.875rem;
      line-height: 1.4;
    }

    .course-nav {
      background: rgba(79, 86, 120, 0.05);
      border-radius: 12px;
      padding: 8px;
      margin-bottom: 40px;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .nav-tab {
      background: transparent;
      color: var(--content-color);
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      /* font-weight: 500; */
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-block;
    }

    .nav-tab.active {
      background: var(--bg-primary);
      color: var(--heading-color);
    }

    .nav-tab:hover {
      background: rgba(79, 86, 120, 0.1);
      color: var(--heading-color);
    }

    .learning-section {
      margin-top: 60px;
    }

    .learning-title {
      color: var(--heading-color);
      font-size: 36px;
      /* font-weight: 600; */
      margin-bottom: 40px;
    }

    .learning-grid {
      display: grid;
      grid-template-columns: repeat(2, minmax(400px, 1fr));
      gap: 32px;
      width: 80%;
    }

    .learning-item {
      display: flex;
      align-items: flex-start;
      gap: 16px;
    }

    .check-icon {
      /* margin-top: 8px; */
      align-self: center;
    }

    .check-icon::after {
      content: '✓';
      color: white;
      font-size: 14px;
      font-weight: bold;
    }

    .learning-text {
      color: var(--content-color);
      font-size: 1rem;
      line-height: 1.6;
    }

    .skills-section {
      margin-top: 80px;
    }

    .skills-title {
      color: var(--heading-color);
      font-size: 36px;
      /* font-weight: 600; */
      margin-bottom: 40px;
    }

    .skills-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      margin-bottom: 60px;
    }

    .skill-tag {
      background: #263260;
      /* color: var(--heading-color); */
      color: #ccc;
      padding: 12px 24px;
      border-radius: 25px;
      font-size: 0.9rem;
      /* font-weight: 500; */
      border: none;
      white-space: nowrap;
    }

    .details-section {
      margin-top: 80px;
    }

    .details-title {
      color: var(--heading-color);
      font-size: 36px;
      /* font-weight: 600; */
      margin-bottom: 40px;
    }

    .details-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 40px;
      margin-bottom: 60px;
      max-width: 70%;
    }

    .detail-item {
      text-align: left;
      width: 250px;
    }

    .detail-icon {
      width: 48px;
      height: 48px;
      background: rgba(60, 170, 86, 0.1);
      border: 1px solid rgba(60, 170, 86, 0.2);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
      font-size: 20px;
      color: #3CAA56;
    }

    .detail-title {
      color: var(--heading-color);
      font-size: 1.1rem;
      /* font-weight: 600; */
      margin-bottom: 8px;
    }

    .detail-description {
      color: var(--content-color);
      font-size: 0.9rem;
      line-height: 1.5;
    }

    .testimonial-section {
      margin-top: 80px;
    }

    .testimonial-title {
      color: var(--heading-color);
      font-size: 36px;
      /* font-weight: 600; */
      margin-bottom: 40px;
    }

    .testimonial-card {
      background: rgba(79, 86, 120, 0.1);
      border: 1px solid #3C4561;
      border-radius: 16px;
      padding: 32px;
      position: relative;
    }

    .testimonial-content {
      color: var(--content-color);
      font-size: 1rem;
      line-height: 1.6;
      font-style: italic;
      /* margin-bottom: 24px; */
    }

    .testimonial-author {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 24px;
    }

    .author-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: linear-gradient(135deg, #3CAA56 0%, #1C2239 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      /* font-weight: 600; */
      font-size: 1.2rem;
    }

    .author-info {
      display: flex;
      flex-direction: column;
    }

    .author-name {
      color: var(--heading-color);
      font-size: 1rem;
      /* font-weight: 600; */
      margin-bottom: 4px;
    }

    .author-title {
      color: var(--content-color);
      font-size: 0.875rem;
    }

    .quote-icon {
      position: absolute;
      top: 24px;
      right: 32px;
      font-size: 2rem;
      color: rgba(60, 170, 86, 0.3);
    }

    .faq-section {
      margin-top: 80px;
    }

    .faq-title {
      color: var(--heading-color);
      font-size: 36px;
      /* font-weight: 600; */
      margin-bottom: 40px;
    }

    .faq-container {
      max-width: 100%;
    }

    .faq-item {
      border-bottom: 1px solid #3C4561;
      margin-bottom: 0;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .faq-item:last-child {
      border-bottom: none;
    }

    .faq-question {
      width: 100%;
      background: transparent;
      border: none;
      /* padding: 24px 28px 24px 0; */
      text-align: left;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: var(--heading-color);
      font-size: 1.1rem;
      /* font-weight: 500; */
      transition: all 0.3s ease;
      position: relative;
    }

    .faq-question::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 20px;
      background: #007bff;
      border-radius: 2px;
    }

    .faq-question span {
      margin-left: 20px;
      flex: 1;
    }

    .faq-arrow {
      font-size: 1.2rem;
      color: var(--content-color);
      transition: transform 0.3s ease, color 0.3s ease;
      flex-shrink: 0;
      margin-left: 16px;
    }

    .faq-item.active .faq-arrow {
      transform: rotate(180deg);
      color: #3CAA56;
    }

    .faq-answer {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.4s ease, padding 0.4s ease;
      background: transparent;
      padding: 0;
    }

    .faq-item.active .faq-answer {
      max-height: 300px;
      padding: 0 0 24px 20px;
    }

    .faq-answer-content {
      color: var(--content-color);
      font-size: 1rem;
      line-height: 1.6;
      padding-top: 8px;
    }

    .faq-highlight {
      /* border-left: 3px solid #3CAA56; */
      padding-left: 16px;
      margin-left: 12px;
    }

    @media (max-width: 768px) {
      .course-main-title {
        font-size: 2rem;
      }

      .course-description {
        max-width: 100%;
        font-size: 1rem;
      }

      .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 24px;
      }

      .course-stats {
        padding: 24px;
      }

      .learning-grid {
        grid-template-columns: 1fr;
        gap: 24px;
        width: 100%;
      }

      .nav-tab {
        padding: 10px 16px;
        font-size: 0.9rem;
      }

      .skills-grid {
        gap: 12px;
      }

      .skill-tag {
        padding: 10px 20px;
        font-size: 0.85rem;
      }

      .details-grid {
        grid-template-columns: 1fr;
        gap: 32px;
      }

      .testimonial-card {
        padding: 24px;
      }
    }

    @media (max-width: 576px) {
      .course-main-title {
        font-size: 1.75rem;
      }

      .google-logo {
        width: 100px;
      }

      .stats-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .skills-section,
      .details-section,
      .testimonial-section,
      .faq-section {
        margin-top: 60px;
      }

      .skills-title,
      .details-title,
      .testimonial-title,
      .faq-title {
        font-size: 1.75rem;
      }

      .faq-question {
        padding: 20px 24px 20px 0;
        font-size: 1rem;
      }

      .faq-item.active .faq-answer {
        padding: 0 0 20px 16px;
      }

      .faq-arrow {
        font-size: 1.1rem;
      }

      .faq-question span {
        margin-left: 16px;
      }
    }
  </style>
</head>

<body>
  <!-- Header Section - Same as index.html -->
  <section class="header-section px-5 py-4">
    <!-- blog and about us -->
    <div class="container-width header-block container-fluid d-flex justify-content-end px-0">
      <div class="d-flex align-items-center menu-links">
        <a href="#" class="text-decoration-none  me-3 nav-links">Blog</a>
        <a class="text-decoration-none nav-links" href="about-us.html"> About us </a>
      </div>
    </div>
    <nav class="nav-section navbar navbar-expand-lg py-4 ">
      <div class="container-fluid container-width px-0">
        <a href="index.html" class="navbar-brand d-flex align-items-center">
          <img class="img-fluid" src="assets/olearn-logo-full.svg" alt="olearn logo" />
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarScroll">
          <span class="bar"></span>
          <span class="bar"></span>
          <span class="bar mb-0"></span>
        </button>
        <div class="collapse navbar-collapse justify-content-start align-self-end" id="navbarScroll">
          <ul class="navbar-nav">
            <li class="nav-item ">
              <a class="nav-link  nav-link-text" href="index.html#features">Features</a>
            </li>
            <li class="nav-item">
              <a class="nav-link  nav-link-text" href="index.html#pricing">Pricing</a>
            </li>
            <li class="nav-item">
              <a class="nav-link nav-link-text" href="index.html#application">Application</a>
            </li>
            <li class="nav-item">
              <a class="nav-link nav-link-text" href="free-ai-courses.html">Free AI Courses</a>
            </li>
            <li class="nav-item">
              <a class="nav-link nav-link-text" href="course-details.html">Course Details</a>
            </li>
          </ul>
        </div>
        <div class="d-flex align-items-center">
          <div class="d-none d-md-block  trail-btn">
            <a class="btn" href="https://app.olearn.ai/login" target="_blank">Start your free trial</a>
          </div>
        </div>
      </div>
    </nav>
  </section>

  <!-- Course Hero Section -->
  <section class="course-hero-section">
    <div class="container">
      <div class="row">
        <div class="col-lg-8">
          <!-- Google Logo -->
          <img src="assets/google-icon.svg" alt="Google" class="google-logo">

          <h1 class="course-main-title">Introduction to AI and Generative AI</h1>

          <p class="course-description">
            Get on the fast track to a career in UX design. In this certificate program, you'll learn in-demand skills and get AI
            training from Google experts. Learn at your own pace, no degree or experience required.
          </p>

          <a href="https://app.olearn.ai/login" class="enroll-btn" target="_blank">Enroll For Free</a>

          <div class="enrollment-count">
            <span class="enrollment-count-digit">1,227,328 </span> already enrolled
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Course Stats Section -->
  <section class="course-content-section">
    <div class="container">
      <div class="course-stats">
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-number">8 Course Series</span>
            <!-- <div class="stat-label">Course Series</div> -->
            <div class="stat-description">Earn a career credential that demonstrates your expertise</div>
          </div>
          <div class="stat-item">
            <span class="stat-number">4.8</span>
            <!-- <div class="stat-label">★</div> -->
            <div class="stat-description">Earn a career credential that demonstrates your expertise</div>
          </div>
          <div class="stat-item">
            <span class="stat-number">AI Roadmap</span>
            <!-- <div class="stat-label"></div> -->
            <div class="stat-description">Earn a career credential that demonstrates your expertise</div>
          </div>
          <div class="stat-item">
            <span class="stat-number">AI Certified</span>
            <!-- <div class="stat-label"></div> -->
            <div class="stat-description">Earn a career credential that demonstrates your expertise</div>
          </div>
        </div>
      </div>

      <!-- What you'll learn Section -->
      <div class="learning-section">
        <h2 class="learning-title">What you'll learn</h2>
        <div class="learning-grid">
          <div class="learning-item">
            <!-- <div class="check-icon"></div> -->
            <img src="assets/green-tick.svg" alt="check-icon" class="check-icon"/>
            <div class="learning-text">
              Understand the fundamentals of effective AI prompting using tools like Gemini, ChatGPT, and Anthropic's Claude
            </div>
          </div>
          <div class="learning-item">
            <!-- <div class="check-icon"></div> -->
             <img src="assets/green-tick.svg" alt="check-icon" class="check-icon"/>
            <div class="learning-text">
              How to think through the lifecycle of a generative AI project, from conception to launch, including how to build effective prompts
            </div>
          </div>
          <div class="learning-item">
            <!-- <div class="check-icon"></div> -->
             <img src="assets/green-tick.svg" alt="check-icon" class="check-icon"/>
            <div class="learning-text">
              Understand the fundamentals of effective AI prompting using tools like Gemini, ChatGPT, and Anthropic's Claude
            </div>
          </div>
          <div class="learning-item">
            <!-- <div class="check-icon"></div> -->
             <img src="assets/green-tick.svg" alt="check-icon" class="check-icon"/>
            <div class="learning-text">
              Understand the fundamentals of effective AI prompting using tools like Gemini, ChatGPT, and Anthropic's Claude
            </div>
          </div>
        </div>
      </div>

      <!-- Skills You'll gain Section -->
      <div class="skills-section">
        <h2 class="skills-title">Skills You'll gain</h2>
        <div class="skills-grid">
          <span class="skill-tag">Prompt Engineering</span>
          <span class="skill-tag">AI Collaboration</span>
          <span class="skill-tag">Workplace Productivity</span>
          <span class="skill-tag">Task Automation</span>
          <span class="skill-tag">Effective Communication with AI</span>
          <span class="skill-tag">Gemini for Workspace</span>
          <span class="skill-tag">AI-Assisted Writing</span>
          <span class="skill-tag">Creative AI Tools</span>
          <span class="skill-tag">Use of ChatGPT, Claude, Gemini</span>
          <span class="skill-tag">Professional Prompting Frameworks</span>
        </div>
      </div>

      <!-- Details to know Section -->
      <div class="details-section">
        <h2 class="details-title">Details to know</h2>
        <div class="details-grid">
          <div class="detail-item">
            <div class="detail-icon">
              <!-- <i class="fas fa-certificate"></i> -->
               <img src="assets/certificate.svg" alt="certificate"/>
            </div>
            <div class="detail-title">Shareable certificate</div>
            <div class="detail-description">Earn a career credential that demonstrates your expertise</div>
          </div>
          <div class="detail-item">
            <div class="detail-icon">
              <!-- <i class="fas fa-globe"></i> -->
               <img src="assets/language.svg" alt="language"/>
            </div>
            <div class="detail-title">Taught in English</div>
            <div class="detail-description">Earn a career credential that demonstrates your expertise</div>
          </div>
          <div class="detail-item">
            <div class="detail-icon">
              <!-- <i class="fas fa-route"></i> -->
               <img src="assets/roadmap.svg" alt="roadmap"/>
            </div>
            <div class="detail-title">AI Roadmap</div>
            <div class="detail-description">Earn a career credential that demonstrates your expertise</div>
          </div>
        </div>
      </div>

      <!-- Testimonial Section -->
      <div class="testimonial-section">
        <h2 class="testimonial-title">Why people choose Olearn for their career</h2>
        <div class="testimonial-card">
          <div class="quote-icon">"</div>
          <div class="testimonial-author">
            <div class="author-avatar">NJ</div>
            <div class="author-info">
              <div class="author-name">Norman Juliet</div>
              <div class="author-title">Commerce Graduate</div>
            </div>
          </div>
          <div class="testimonial-content">
            I directly applied the concepts and skills I learned from my courses to an exciting new project at work.
          </div>
        </div>
      </div>

      <!-- FAQ Section -->
      <div class="faq-section">
        <h2 class="faq-title">FAQ</h2>
        <div class="faq-container">
          <div class="faq-item">
            <button class="faq-question" onclick="toggleFAQ(this)">
              <span>What is TuringLearn AI?</span>
              <i class="fas fa-chevron-down faq-arrow"></i>
            </button>
            <div class="faq-answer">
              <div class="faq-answer-content">
                <div class="faq-highlight">
                  TuringLearn AI is a cutting-edge learning and career development platform that uses artificial intelligence to deliver personalized insights, interactive practice sessions, and actionable feedback for users aiming to upskill and succeed in their careers.
                </div>
              </div>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question" onclick="toggleFAQ(this)">
              <span>Who can use TuringLearn AI?</span>
              <i class="fas fa-chevron-down faq-arrow"></i>
            </button>
            <div class="faq-answer">
              <div class="faq-answer-content">
                TuringLearn AI is designed for professionals, students, and anyone looking to enhance their skills and advance their careers. Whether you're a beginner or an experienced professional, our platform adapts to your learning needs and provides personalized guidance.
              </div>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question" onclick="toggleFAQ(this)">
              <span>How does TuringLearn AI personalize learning?</span>
              <i class="fas fa-chevron-down faq-arrow"></i>
            </button>
            <div class="faq-answer">
              <div class="faq-answer-content">
                Our AI algorithms analyze your learning patterns, strengths, and areas for improvement to create customized learning paths. The platform adapts content difficulty, suggests relevant resources, and provides targeted feedback based on your individual progress and goals.
              </div>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question" onclick="toggleFAQ(this)">
              <span>What features make TuringLearn AI unique?</span>
              <i class="fas fa-chevron-down faq-arrow"></i>
            </button>
            <div class="faq-answer">
              <div class="faq-answer-content">
                TuringLearn AI offers AI-powered personalization, interactive practice sessions, real-time feedback, virtual interview simulations, comprehensive skill assessments, and career guidance. Our platform combines cutting-edge technology with proven educational methodologies.
              </div>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question" onclick="toggleFAQ(this)">
              <span>How does the virtual interview feature work?</span>
              <i class="fas fa-chevron-down faq-arrow"></i>
            </button>
            <div class="faq-answer">
              <div class="faq-answer-content">
                Our virtual interview feature uses AI to simulate real interview scenarios. It provides practice questions, analyzes your responses, offers feedback on communication skills, and helps you prepare for various types of interviews in your field.
              </div>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question" onclick="toggleFAQ(this)">
              <span>What types of mock tests are available?</span>
              <i class="fas fa-chevron-down faq-arrow"></i>
            </button>
            <div class="faq-answer">
              <div class="faq-answer-content">
                We offer a wide variety of mock tests including technical assessments, aptitude tests, industry-specific evaluations, coding challenges, and soft skills assessments. All tests are designed to mirror real-world scenarios and provide detailed performance analytics.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer Section - Same as index.html -->
  <footer class="footer-section container-width container-fluid px-5  py-4   mb-5">
    <h4 class="mb-4 text-center text-md-start mb-5">
      <img src="assets/olearn-logo-full.svg" alt="Olearn logo">
    </h4>
    <div class="row text-center text-md-start ">
      <div class="col-sm-6 col-md-3 mb-4">
        <div class="d-flex flex-column gap-2">
          <h5 class="footer-headings">About Olearn</h5>
          <a href="#" class="mt-2 text-decoration-none footer-links">Company Overview</a>
          <a href="#" class="text-decoration-none footer-links">Careers</a>
          <a href="#" class="text-decoration-none footer-links">Press & Media</a>
          <a href="#" class="text-decoration-none footer-links">Testimonials</a>
        </div>
      </div>
      <div class="col-sm-6 col-md-3 mb-4">
        <div class="d-flex flex-column gap-2">
          <h5 class="footer-headings">Resources</h5>
          <a href="#" class="mt-2 text-decoration-none footer-links">Blog</a>
          <a href="#" class="text-decoration-none footer-links">Help Center</a>
          <a href="#" class="text-decoration-none footer-links">Webinars & Events</a>
          <a href="#" class="text-decoration-none footer-links">Case studies</a>
        </div>
      </div>
      <div class="col-sm-6 col-md-3 mb-4">
        <div class="d-flex flex-column gap-2">
          <h5 class="footer-headings">Support & Content</h5>
          <a href="#" class="mt-2 text-decoration-none footer-links">Contact Us</a>
          <a href="#" class="text-decoration-none footer-links">Technical Support</a>
          <a href="#" class="text-decoration-none footer-links">Feedback</a>
          <a href="#" class="text-decoration-none footer-links">Community Forum</a>
        </div>
      </div>
      <div class="col-sm-6 col-md-3 mb-4">
        <div class="d-flex flex-column gap-2">
          <h5 class="footer-headings">Connect</h5>
          <a href="https://www.instagram.com/olearn.ai/" class="mt-2 text-decoration-none footer-links"><i
              class="fa-brands fa-instagram"></i><span class="ms-2">Instagram</span></a>
          <a href="#" class="text-decoration-none footer-links"><i class="fa-brands fa-facebook"></i><span
              class="ms-2">Facebook</span></a>
          <a href="#" class="text-decoration-none footer-links"><i class="fa-brands fa-square-x-twitter"></i><span
              class="ms-2">Twitter / X</span></a>
          <a href="#" class="text-decoration-none footer-links"><i class="fa-brands fa-linkedin"></i><span
              class="ms-2">LinkedIn</span>
        </div>
      </div>
    </div>
    </div>
    <hr>
    <div class="d-flex flex-column flex-md-row justify-content-between text-center text-md-start">
      <div class="copywrite-container left-section mb-2 mb-md-0 ">
        <span>&#169; Olearn</span>
        <span class="mx-1">·</span>
        <span>All rights reserved</span>
      </div>
      <div class="copywrite-container right-section ">
        <a href="terms.html" class=" text-decoration-none me-3">Terms of Use</a>
        <a href="privacy.html" class=" text-decoration-none me-3">Privacy Policy</a>
        <!-- <a href="#" class=" text-decoration-none">Security</a> -->
      </div>
    </div>
  </footer>

  <script src="https://unpkg.com/typeit@8.7.1/dist/index.umd.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
  <script src="script/script.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
    integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
    crossorigin="anonymous"></script>

  <script>
    function toggleFAQ(button) {
      const faqItem = button.parentElement;
      const isActive = faqItem.classList.contains('active');

      // Close all FAQ items
      document.querySelectorAll('.faq-item').forEach(item => {
        item.classList.remove('active');
      });

      // If the clicked item wasn't active, open it
      if (!isActive) {
        faqItem.classList.add('active');
      }
    }

    // Ensure all FAQ items are closed by default
    document.addEventListener('DOMContentLoaded', function() {
      document.querySelectorAll('.faq-item').forEach(item => {
        item.classList.remove('active');
      });
    });
  </script>

</body>

</html>