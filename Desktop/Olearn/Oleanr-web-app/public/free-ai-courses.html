<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />

  <title>Free AI Courses - Olearn.ai | Learn AI, Get Certified, No Strings Attached</title>
  <meta name="description"
    content="Master AI and prompt engineering with free certification courses. Learn ChatGPT, Anthropic, Gemini & more with hands-on projects and industry-standard tools.">
  <meta name="keywords"
    content="free AI courses, AI certification, prompt engineering, ChatGPT course, Gemini course, AI learning, free certification">
  <meta name="author" content="Olearn.ai">
  <meta name="robots" content="index, follow">

  <!-- Open Graph (OG) Meta Tags for Social Media -->
  <meta property="og:title" content="Free AI Courses - Olearn.ai">
  <meta property="og:description"
    content="Master AI and prompt engineering with free certification courses. Learn ChatGPT, Anthropic, Gemini & more.">
  <meta property="og:image" content="https://olearn.ai/assets/olearn-logo.svg">
  <meta property="og:url" content="https://olearn.ai/free-ai-courses.html">
  <meta property="og:type" content="website">

  <!-- Canonical URL -->
  <link rel="canonical" href="https://olearn.ai/free-ai-courses.html">

  <link rel="shortcut icon" href="assets/olearn-logo.svg" type="image/x-icon">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
    integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link rel="stylesheet" href="style/style.css" />
  <link rel="stylesheet" href="style/ai-courses.css">

  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-database-compat.js"></script>

  <!-- Firebase Configuration -->
  <script src="script/firebase-config.js"></script>

</head>

<body>
  <!-- Header Section - Same as index.html -->
  <section class="header-section px-5 py-4">
    <!-- blog and about us -->
    <div class="container-width header-block container-fluid d-flex justify-content-end px-0">
      <div class="d-flex align-items-center menu-links">
        <a href="#" class="text-decoration-none  me-3 nav-links">Blog</a>
        <a class="text-decoration-none nav-links" href="about-us.html"> About us </a>
      </div>
    </div>
    <nav class="nav-section navbar navbar-expand-lg py-4 ">
      <div class="container-fluid container-width px-0">
        <a href="index.html" class="navbar-brand d-flex align-items-center">
          <img class="img-fluid" src="assets/olearn-logo-full.svg" alt="olearn logo" />
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarScroll">
          <span class="bar"></span>
          <span class="bar"></span>
          <span class="bar mb-0"></span>
        </button>
        <div class="collapse navbar-collapse justify-content-start align-self-end" id="navbarScroll">
          <ul class="navbar-nav">
            <li class="nav-item ">
              <a class="nav-link  nav-link-text" href="index.html#features">Features</a>
            </li>
            <li class="nav-item">
              <a class="nav-link  nav-link-text" href="index.html#pricing">Pricing</a>
            </li>
            <li class="nav-item">
              <a class="nav-link nav-link-text" href="index.html#application">Application</a>
            </li>
            <li class="nav-item">
              <a class="nav-link nav-link-text" href="free-ai-courses.html">Free AI Courses</a>
            </li>
            <li class="nav-item">
              <a class="nav-link nav-link-text" href="course-details.html">Course Details</a>
            </li>
          </ul>
        </div>
        <div class="d-flex align-items-center">
          <div class="d-none d-md-block  trail-btn">
            <a class="btn" href="https://app.olearn.ai/login" target="_blank">Start your free trial</a>
          </div>
        </div>
      </div>
    </nav>
  </section>

  <!-- Hero Section -->
  <section class="courses-hero-section text-center">
    <div class="container">
      <h1 class="hero-title text-center">Learn AI. Get Certified.<br>No Strings Attached.</h1>
      <p class="hero-subtitle mx-auto">
        Ready to master prompt engineering with tools like ChatGPT,<br>
        Anthropic, Gemini & more?
      </p>
    </div>
  </section>

  <!-- Courses Section -->
  <section class="py-5">
    <div class="container courses-container">
      <!-- Loading indicator -->
      <div id="loading-indicator" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading courses...</span>
        </div>
        <p class="mt-3">Loading courses...</p>
      </div>

      <!-- Error message -->
      <div id="error-message" class="alert alert-danger text-center" style="display: none;">
        <h5>Unable to load courses</h5>
        <p>Please try refreshing the page or contact support if the problem persists.</p>
      </div>

      <!-- Courses container -->
      <div id="courses-container" class="row g-4 justify-content-center" style="display: none;">
        <!-- Dynamic course cards will be inserted here -->
      </div>
    </div>
  </section>

  <!-- Footer Section - Same as index.html -->
  <footer class="footer-section container-width container-fluid px-5  py-4   mb-5">
    <h4 class="mb-4 text-center text-md-start mb-5">
      <img src="assets/olearn-logo-full.svg" alt="Olearn logo">
    </h4>
    <div class="row text-center text-md-start ">
      <div class="col-sm-6 col-md-3 mb-4">
        <div class="d-flex flex-column gap-2">
          <h5 class="footer-headings">About Olearn</h5>
          <a href="#" class="mt-2 text-decoration-none footer-links">Company Overview</a>
          <a href="#" class="text-decoration-none footer-links">Careers</a>
          <a href="#" class="text-decoration-none footer-links">Press & Media</a>
          <a href="#" class="text-decoration-none footer-links">Testimonials</a>
        </div>
      </div>
      <div class="col-sm-6 col-md-3 mb-4">
        <div class="d-flex flex-column gap-2">
          <h5 class="footer-headings">Resources</h5>
          <a href="#" class="mt-2 text-decoration-none footer-links">Blog</a>
          <a href="#" class="text-decoration-none footer-links">Help Center</a>
          <a href="#" class="text-decoration-none footer-links">Webinars & Events</a>
          <a href="#" class="text-decoration-none footer-links">Case studies</a>
        </div>
      </div>
      <div class="col-sm-6 col-md-3 mb-4">
        <div class="d-flex flex-column gap-2">
          <h5 class="footer-headings">Support & Content</h5>
          <a href="#" class="mt-2 text-decoration-none footer-links">Contact Us</a>
          <a href="#" class="text-decoration-none footer-links">Technical Support</a>
          <a href="#" class="text-decoration-none footer-links">Feedback</a>
          <a href="#" class="text-decoration-none footer-links">Community Forum</a>
        </div>
      </div>
      <div class="col-sm-6 col-md-3 mb-4">
        <div class="d-flex flex-column gap-2">
          <h5 class="footer-headings">Connect</h5>
          <a href="https://www.instagram.com/olearn.ai/" class="mt-2 text-decoration-none footer-links"><i
              class="fa-brands fa-instagram"></i><span class="ms-2">Instagram</span></a>
          <a href="#" class="text-decoration-none footer-links"><i class="fa-brands fa-facebook"></i><span
              class="ms-2">Facebook</span></a>
          <a href="#" class="text-decoration-none footer-links"><i class="fa-brands fa-square-x-twitter"></i><span
              class="ms-2">Twitter / X</span></a>
          <a href="#" class="text-decoration-none footer-links"><i class="fa-brands fa-linkedin"></i><span
              class="ms-2">LinkedIn</span>
        </div>
      </div>
    </div>
    </div>
    <hr>
    <div class="d-flex flex-column flex-md-row justify-content-between text-center text-md-start">
      <div class="copywrite-container left-section mb-2 mb-md-0 ">
        <span>&#169; Olearn</span>
        <span class="mx-1">·</span>
        <span>All rights reserved</span>
      </div>
      <div class="copywrite-container right-section ">
        <a href="terms.html" class=" text-decoration-none me-3">Terms of Use</a>
        <a href="privacy.html" class=" text-decoration-none me-3">Privacy Policy</a>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
    integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
    crossorigin="anonymous"></script>

  <script>
  

    // Function to create course card HTML
    function createCourseCard(course) {
      const enrollmentCount = course.enrollments || 0;

      return `
        <div class="col-lg-4 col-md-6">
          <div class="course-card">
            <div class="course-card-content">
              <h3 class="course-title">${course.title || 'Course Title'}</h3>
              <p class="course-description">
                ${course.description || 'Course description not available.'}
              </p>
              <a href="https://app.olearn.ai/login" class="course-btn" target="_blank">Start Free Certification</a>
            </div>
            <div class="enrollment-info">
              <div class="enrollment-avatars">
                <img src="assets/Cartoon.svg" alt="Student" class="enrollment-avatar">
                <img src="assets/Cartoon.svg" alt="Student" class="enrollment-avatar">
                <img src="assets/Cartoon.svg" alt="Student" class="enrollment-avatar">
              </div>
              <span class="enrollment-text">
                <span class="enrollment-count">${enrollmentCount}</span> ${enrollmentCount === 1 ? 'student enrolled' : 'students enrolled'}
              </span>
            </div>
          </div>
        </div>
      `;
    }

    // Function to display courses in the container
    function displayCourses(courses) {
      console.log(courses)
      const coursesContainer = document.getElementById('courses-container');

      // Clear existing content
      coursesContainer.innerHTML = '';

      // Create course cards
      if (Array.isArray(courses)) {
        // If courses is an array (like fallbackCourses)
        courses.forEach(course => {
          coursesContainer.innerHTML += createCourseCard(course);
        });
      } else {
        // If courses is an object (like Firebase data)
        Object.values(courses).forEach(course => {
          coursesContainer.innerHTML += createCourseCard(course);
        });
      }

      // Show courses container
      coursesContainer.style.display = 'flex';
    }

    // Function to load courses from Firebase
    function loadCourses() {
      const loadingIndicator = document.getElementById('loading-indicator');
      const errorMessage = document.getElementById('error-message');
      const coursesContainer = document.getElementById('courses-container');

      // Show loading indicator
      loadingIndicator.style.display = 'block';
      errorMessage.style.display = 'none';
      coursesContainer.style.display = 'none';

      // Check if Firebase config is valid
      if (!window.firebaseConfig || window.firebaseConfig.apiKey === 'YOUR_API_KEY_HERE') {
        console.warn('Firebase configuration not set. Using fallback data.');

        // Hide loading indicator
        loadingIndicator.style.display = 'none';

        // Use fallback data
        displayCourses(window.fallbackCourses);
        return;
      }

      try {
        // Initialize Firebase
        if (!firebase.apps.length) {
          firebase.initializeApp(window.firebaseConfig);
        }

        const database = firebase.database();

        // Fetch courses from Firebase
        database.ref('toolData/courses/list').once('value')
          .then((snapshot) => {
            // Hide loading indicator
            loadingIndicator.style.display = 'none';

            const courses = snapshot.val();

            if (courses && Object.keys(courses).length > 0) {
              // Display the courses
              displayCourses(courses);
            } else {
              console.warn('No courses found in Firebase. Using fallback data.');
              // Use fallback data if no courses found
              displayCourses(window.fallbackCourses);
            }
          })
          .catch((error) => {
            console.error('Error fetching courses from Firebase:', error);

            // Hide loading indicator
            loadingIndicator.style.display = 'none';

            // Use fallback data on error
            displayCourses(window.fallbackCourses);
          });
      } catch (error) {
        console.error('Error initializing Firebase:', error);

        // Hide loading indicator
        loadingIndicator.style.display = 'none';

        // Use fallback data on error
        displayCourses(window.fallbackCourses);
      }
    }

    // Load courses when the page loads
    document.addEventListener('DOMContentLoaded', loadCourses);
  </script>

</body>

</html>
