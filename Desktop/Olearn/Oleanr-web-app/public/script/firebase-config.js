// Firebase Configuration
// Replace these values with your actual Firebase project configuration
// You can find these values in your Firebase Console > Project Settings > General > Your apps

const firebaseConfig = {
  // TODO: Replace with your actual Firebase configuration
  apiKey: "YOUR_API_KEY_HERE",
  authDomain: "hashinclude2024.firebaseapp.com",
  databaseURL: "https://hashinclude2024-default-rtdb.firebaseio.com",
  projectId: "hashinclude2024",
  storageBucket: "hashinclude2024.appspot.com",
  messagingSenderId: "YOUR_MESSAGING_SENDER_ID_HERE",
  appId: "YOUR_APP_ID_HERE"
};

// Fallback course data in case Firebase is not available or configured
const fallbackCourses = [
  {
    title: "Introduction to AI and Generative AI",
    description: "Learn the fundamentals of AI/UX design with hands-on projects and industry-standard tools. Master the core concepts of artificial intelligence and generative AI technologies.",
    enrollments: 1227328
  },
  {
    title: "AI Tools (<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>)",
    description: "Learn the fundamentals of AI/UX design with hands-on projects and industry-standard tools. Explore popular AI platforms and their applications.",
    enrollments: 1227328
  },
  {
    title: "Chat GPT Mastery",
    description: "Learn the fundamentals of AI/UX design with hands-on projects and industry-standard tools. Deep dive into ChatGPT capabilities and prompt engineering.",
    enrollments: 200
  },
  {
    title: "Prompt Engineering Fundamentals",
    description: "Master the art of crafting effective prompts for AI systems. Learn techniques to get better results from AI tools and improve your productivity.",
    enrollments: 85432
  },
  {
    title: "AI Ethics and Responsible AI",
    description: "Understand the ethical implications of AI technology and learn how to develop and deploy AI systems responsibly in various industries.",
    enrollments: 45678
  },
  {
    title: "Machine Learning Basics",
    description: "Get started with machine learning concepts, algorithms, and practical applications. Perfect for beginners looking to enter the AI field.",
    enrollments: 156789
  }
];

// Export configuration and fallback data
window.firebaseConfig = firebaseConfig;
window.fallbackCourses = fallbackCourses;
