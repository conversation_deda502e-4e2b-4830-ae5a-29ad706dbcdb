// const typed = new Typed('#typed-text', {
//     strings: [`<span class='hero-highlight'>90%</span>  of learners improve problem-solving with <PERSON><PERSON><PERSON> and feel job-ready!`, ' AI mock interviews help candidates land jobs 2x faster on their first attempt!', `75% of students score higher and boost their confidence with Olearn's AI training!`],
//     typeSpeed: 300,
//     backSpeed: 30,
//     smartBackspace: true,
//     contentType: 'html'
// });

new TypeIt("#typed-text", {
    strings: [`<span class='hero-highlight'>90%</span>  of learners improve problem-solving with <PERSON><PERSON><PERSON> and feel job-ready!`, ' AI mock interviews help candidates land jobs <span class="hero-highlight">2x</span> faster on their first attempt!', ` <span class='hero-highlight'> 75% </span> of students score higher and boost their confidence with Olearn's AI training!`],
    speed: 50,
    lifeLike: true,         // Key feature for smoothness
    cursor: true,
    cursorSpeed: 1000,
    waitUntilVisible: true,
    loop: true,
    html: true,
    startDelay: 250,
    startDelete: true,
    breakLines: false,
    nextStringDelay: 1000
}).go();



function executeOnMobile() {
    if (window.innerWidth < 576) {
        var swiper = new Swiper(".mySwiper", {
            slidesPerView: 1,

            spaceBetween: 30,
            pagination: {
                el: ".swiper-pagination",
                clickable: true,
            },
        });
    }
}

// Call the function on page load
executeOnMobile();

// Optional: Run the function on window resize
window.addEventListener("resize", executeOnMobile);