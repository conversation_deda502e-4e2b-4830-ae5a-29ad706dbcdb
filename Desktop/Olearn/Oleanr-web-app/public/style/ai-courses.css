    .courses-hero-section {
      padding: 120px 0;
      /* background: linear-gradient(135deg, var(--bg-primary) 0%, #1a2040 100%); */
      background-color: #171C2F;
      max-width: 100vw;
    }

    .hero-title {
      font-size: 4rem;
      font-weight: 700;
      background: linear-gradient(180deg, #FFFFFF 0%, #BABABA 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      line-height: 1.2;
      margin-bottom: 30px;
    }

    .hero-subtitle {
        font-size: 1.45rem;
        color: var(--content-color);
        line-height: 1.6;
        font-weight: 100;
    }

    .course-card {
      /* background: linear-gradient(145deg, #2A3147 0%, #1E2538 100%); */
      background-color: #1C2239;
      border: 1px solid #3C4561;
      border-radius: 24px;
      padding: 0;
      height: 100%;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      /* max-width: 380px; */
      margin: 10px 0px 30px 0px;
    }

    .course-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(145deg, rgba(60, 170, 86, 0.05) 0%, rgba(28, 34, 57, 0.05) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
      border-radius: 24px;
    }

    .course-card:hover::before {
      opacity: 1;
    }

    .course-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      border-color: #4A5568;
    }

    .course-card-content {
      padding: 32px 28px 24px 28px;
      flex: 1;
      display: flex;
      flex-direction: column;
      position: relative;
    }

    .course-title {
      color: var(--heading-color);
      /* font-size: 1.375rem; */
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 16px;
      line-height: 1.3;
      position: relative;
      z-index: 1;
    }

    .course-description {
      color: var(--content-color);
      font-size: 0.95rem;
      line-height: 1.5;
      margin-bottom: 24px;
      position: relative;
      z-index: 1;
      /* flex: 1; */
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .course-btn {
      background: linear-gradient(135deg, #1C2239 20%, #3CAA56 80%);
      color: #FFFFFF;
      border: 1px solid #4A5568;
      border-radius: 25px;
      padding: 12px 32px;
      /* font-weight: 400; */
      font-size: 0.9rem;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
      text-align: center;
      position: absolute;
      bottom: 20px;
      z-index: 1;
      width: fit-content;
      margin: 0 auto 0 0;
    }

    .course-btn:hover {
      /* background: linear-gradient(135deg, #45B863 0%, #232A47 100%); */
      /* color: white; */
      transform: translateY(-2px);
      /* box-shadow: 0 6px 20px rgba(60, 170, 86, 0.3); */
    }

    .enrollment-info {
      display: flex;
      align-items: center;
      /* gap: 12px; */
      /* background: linear-gradient(135deg, rgba(60, 170, 86, 0.15) 0%, rgba(28, 34, 57, 0.15) 100%); */
      background: linear-gradient(135deg, #1C2239 0%, #345784 100%);
      padding: 20px 28px;
      border-top: 1px solid rgba(60, 170, 86, 0.2);
      position: relative;
      z-index: 1;
      margin-top: auto;
    }

    .enrollment-avatars {
      display: flex;
      margin-right: 8px;
    }

    .enrollment-avatar {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      border: 2px solid #2A3147;
      margin-left: -6px;
    }

    .enrollment-avatar:first-child {
      margin-left: 0;
    }

    .enrollment-text {
      color: var(--content-color);
      font-size: 1rem;
      font-weight: 400;
    }

    .enrollment-count {
      color: var(--heading-color);
      font-weight: 600;
    }

    .courses-container {
      /* max-width: 1200px; */
      /* margin: 0 auto; */
      margin: 20px 0px 50px 0px;
      max-width: 100%;
      padding: 0px 30px;
    }

    @media (max-width: 768px) {
      .courses-hero-section {
        padding: 80px 0;
      }

      .hero-title {
        font-size: 2.5rem;
      }

      .hero-subtitle {
        font-size: 1.1rem;
      }

      .course-card {
        margin-bottom: 30px;
        max-width: 100%;
      }

      .course-card-content {
        padding: 28px 24px 20px 24px;
      }

      .enrollment-info {
        padding: 14px 24px;
      }

      .course-title{
        font-size: 20px;
      }
    }

    @media (max-width: 576px) {
      .hero-title {
        font-size: 2rem;
      }

      .hero-subtitle {
        font-size: 1rem;
      }

      .course-card-content {
        padding: 24px 20px 18px 20px;
      }

      .enrollment-info {
        padding: 12px 20px;
      }
    }
  