.pricing-section {
  background-color: #050A20;
}

.pricing-title {
  color: #FFFFFF;
}

.pricing-container {
  max-width: 100%;
  padding: 20px;
  display: flex;
  flex-wrap: wrap;
  /* Allow wrapping for smaller screens */
  gap: 20px;
  /* Add gap between cards */
  justify-content: center;
  position: relative;
  margin-top: 61px;
  /* Center cards horizontally */
}





.pricing-card {
  flex: 1 1 calc(25% - 20px);
  /* Flex basis for 4 cards per row on large screens */
  border-radius: 16px;
  color: #FFFFFF;
  padding: 24px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.pricing-card.popular {
  background: radial-gradient(circle, rgba(30, 29, 30, 1) 27%, rgba(44, 37, 60, 1) 83%);
  transform: scale(1.02);
  z-index: 1;
}

.pricing-card.popular .description {
  color: #FFFFFF;
}

.popular-badge {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: radial-gradient(circle, rgba(30, 29, 30, 1) 27%, rgba(44, 37, 60, 1) 83%);
  color: #FFFFFF;
  text-align: center;
  padding: 6px 25px;
  font-size: 12px;
  font-weight: 300;
  letter-spacing: 0.5px;
  border-radius: 16px;
  box-shadow: 0px 1px 4px 0px black;
}

.credit-badge {
  position: absolute;
  bottom: 11px;
  gap: 12px;
  background: rgb(83, 72, 31);
  background: linear-gradient(98deg, rgba(83, 72, 31, 1) 1%, rgba(23, 23, 23, 1) 68%);
  padding: 1px 29px 1px 9px;
  border-radius: 17px;
}

.credit-badge img {
  width: 24px;
}

span.credit-amount {
  font-size: 22px;
}

span.credit-text {
  font-size: 12px;
  color: #CECECE;
  font-weight: 100;
}

.card-header {
  margin-bottom: 20px;
  padding-top: 10px;
}

.plan-badge {
  border: 1px solid #252A49;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 22px;
  font-size: 14px;
  padding: 5px 18px 4px 7px;
  text-transform: uppercase;

}

.plan-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hobby {
  color: #CECECE;
  background: rgb(13, 19, 34);
  background: linear-gradient(266deg, rgba(13, 19, 34, 1) 0%, rgba(12, 38, 44, 1) 91%);
}


.pro {
  color: #fbbf24;
}

.enterprise {
  color: #60a5fa;
}

.users-badge {
  padding: 8px 21px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  background: linear-gradient(266deg, rgba(13, 17, 34, 1) 0%, rgba(23, 34, 46, 1) 76%);
  border: 1px solid #1c223c;
}


.price-section {
  margin-bottom: 24px;
}

.price {
  font-size: 36px;
  margin-bottom: 12px;
}

.price i {
  font-size: 25px;
}

.description {
  color: #94a3b8;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 20px;
  min-height: 80px;
  font-weight: 300;
}

.signup-btn {
  width: 100%;
  background-color: #1e293b;
  color: white;
  border: none;
  border-radius: 30px;
  padding: 12px;
  font-weight: 600;
  margin-bottom: 24px;
  transition: all 0.3s ease;
  font-weight: 200;
}

.signup-btn:hover {
  background-color: #334155;
}

.signup-btn.primary {
  background-color: #4f46e5;
}

.signup-btn.primary:hover {
  background-color: #4338ca;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
  min-height: 200px;
}

.enterprise-list span {
  width: 201px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
}

.check-icon {
  width: 28px;
  height: 28px;
  background: url(../assets/tick.svg);

}

/* Responsive Styles */
@media (max-width: 1024px) {
  .pricing-card {
    flex: 1 1 calc(50% - 20px);
    /* 2 cards per row on tablets */
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .pricing-card {
    flex: 1 1 100%;
    /* 1 card per row on smaller tablets */
    max-width: 400px;
    margin-bottom: 20px;
  }

  .pricing-title.text-center {
    max-width: 80%;
    margin: auto;
  }

  .features-list {
    min-height: 270px;
  }
}

@media (max-width: 576px) {
  .features-list {
    min-height: 250px;
  }
}

@media (max-width: 480px) {
  .pricing-card {
    padding: 16px;
  }

  .price {
    font-size: 28px;
  }

  .description {
    font-size: 14px;
    min-height: auto;
  }

  .features-list {
    min-height: 280px;
  }
}

.swiper-slide:nth-child(2n) {
  width: 60%;
}

.swiper-slide:nth-child(3n) {
  width: 40%;
}