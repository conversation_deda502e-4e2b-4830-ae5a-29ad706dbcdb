:root {
    --heading-color: #EDEEF3;
    /* For Main and Secondary Headings */
    --content-color: #9DA1B3;
    /* For Paragraphs and Content */
    --footer-subheading-color: #D3D6E2;
    /* For Footer Subheadings */
    --bg-primary: #171C2F;
    /* Default background for the entire page */
    --bg-highlight: #050A20;
    /* Background for Pricing and Contact sections */
    --text-highlight: #9D9DF6;
    --contact-heading: #111D15;
    --contact-text: #666666;
}

.text-yellow {
    color: #F8DA64;
}

/* font style */
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');


body {
    background-color: var(--bg-primary);
    font-family: 'DM Sans', sans-serif;

}





.action-button {
    border: 1px solid #4F5678;
    color: #BEC2D3;
    background-color: var(--bg-primary) !important;
    padding: 7px 20px;
    font-size: 16px;
}

.trail-btn a {
    border-radius: 11px;
    background: #2E71E5;
    color: #fff;
    padding: 9px 20px;
}

.trail-btn a:hover {
    background: #2E71E5;
    color: #fff;

}


.text-content {
    color: var(--content-color);
}

.primary-text {
    color: var(--heading-color);
}

.section-level-heading {
    color: var(--heading-color);
    font-size: 2.44rem;
    font-weight: 700;
}

/* responsive */




.section-highlight {
    color: var(--text-highlight);
}

.section-levl-subheading {
    color: var(--content-color);
    font-weight: 400;
    font-size: 20px;
    max-width: 65%;
    margin: 30px auto;
}






.header-block .nav-links {
    color: #9198B7;
}



nav {
    border-bottom: 1px solid #3C4561;
    max-width: 95%;
    margin: auto;
}


.menu-links {
    margin-right: 30px;
}

/* navbar */
.menu-links a {
    color: var(--content-color);
}

.nav-link-text {
    color: var(--content-color);
}

.nav-link-text:focus {
    color: var(--content-color);
}

.nav-link-text:hover {
    color: var(--content-color);
}

li.nav-item {
    margin-left: 14px;
}

.dropdown-text {
    color: var(--content-color);
    background-color: var(--bg-primary);
}

.dropdown-text,
.dropdown-text:hover,
.dropdown-text:focus {
    color: var(--content-color) !important;
    background-color: var(--bg-primary) !important;
}

/* hero-section */
.hero-section {
    margin-top: 4%;
}

.primary-heading {
    color: var(--heading-color);
    max-width: 45%;
    font-weight: 700;
    font-size: 3rem;
    margin: 0 auto;
    text-align: center;
    font-weight: 500;
}

.secondary-heading {
    color: var(--heading-color);

}





.hero-text {
    font-size: 1.8rem;
    max-width: 58%;
    color: var(--content-color);
    font-weight: 100;
    line-height: 150%;
}

.hero-highlight {
    color: #89A76B;
}

/* keyfeatures */

.feature-grid .row {
    padding-left: 4%;
}

.each-capability-block {
    padding-left: 13%;
}

.feature-content {
    font-weight: 200;
    font-size: 18px;
    max-width: 70%;
}

.feature-title {
    font-weight: 700;
    font-size: 1.5rem;
}



/* student-review section */
.student-review-card {
    border: 1px solid #4F5678;
    border-radius: 8px;
    border-radius: 20px;
    background-color: var(--bg-primary) !important;
    padding: 0 22px 20px 22px;
    height: 375px;
}

.card-text {
    margin-top: 20px;
    font-weight: 100;
    font-size: 18px;
    color: var(--heading-color);
}



.student-name {
    color: var(--footer-subheading-color);
    font-weight: 400;
    font-size: 1rem;
}

.designation {
    color: var(--content-color);
    font-size: 14px;
    font-weight: 400;
}








/* contact us  */
.contact-section {
    background-color: var(--bg-highlight);
}

.contact-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 12px;
    background-color: white;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}



.contact-heading {
    color: var(--contact-heading);
    font-weight: 600;
    font-size: 1.125rem;
}

.contact-details {
    color: var(--contact-text);
    font-weight: 400;
    font-size: 1rem;
    margin-bottom: 0;
}

.find-us-inner-container {
    max-width: 95%;
    margin: 0 auto;
}

.find {
    color: #FFFFFF;
    font-size: 1.5rem;
    font-weight: 300;
    margin: 20px 0 30px 88px;
}

input.form-control.contact-input {
    height: 48px;
}

.contact-form {
    color: var(--heading-color);
    font-weight: 400;
    font-size: 1.125rem;
}

.contact-touch {
    color: var(--heading-color);
    font-weight: 400;
    font-size: 2rem;
}

.contact-text {
    color: #DBDBDB;
    font-weight: 400;
    font-size: 1rem;
    margin: 12px 0px 23px 0;
}







/* learning content */

.learning-content {
    font-weight: 400;
    font-size: 1rem;
    max-width: 55%;
    color: var(--content-color);
}

.custom-card {
    color: white;
    padding: 24px;
    border-radius: 8px;
    height: 100%;
    max-width: 380px;
    border: 1px solid #4F5678;
    display: flex;
    flex-direction: column;
}

.learning-heading {
    color: var(--heading-color);
    font-size: 1.25rem;
    font-weight: 700;
}

.learning-text {
    color: var(--content-color);
    font-weight: 400;
    font-size: 1rem;
}

.explore {
    background-color: #171C2F;
    color: var(--footer-subheading-color);
    border: 1px solid #4F5678
}

.swiper-pagination.swiper-pagination-clickable.swiper-pagination-bullets.swiper-pagination-horizontal {
    position: relative;
    bottom: -3px;
}

span.swiper-pagination-bullet {
    width: 11px;
    height: 11px;
}







/* faq-section */
.accordion,
.accordion-item,
.accordion-button,
.accordion-body {
    background-color: var(--bg-primary) !important;
}

button:focus:not(:focus-visible) {
    box-shadow: none;
}

.faq-title {
    color: var(--heading-color);
    font-size: 2.5rem;
    font-weight: 700;
}

.faq-subtext {
    color: var(--content-color);
    font-weight: 400;
    font-size: 1rem;
}

.faq-answer {
    color: var(--content-color);
    padding: 15px;
    font-weight: 400;
    font-size: 1rem;
}

.faq-item {
    position: relative;
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--heading-color) !important;
    width: 100%;
    padding: 24px;
    background: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.faq-item:not(.collapsed) {
    color: var(--heading-color) !important;
}

.faq-item::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 12px;
    /* background-color: #007bff; */
    border-radius: 10px;
}

.accordion-item {
    border: none;
    background: transparent;
}



button.accordion-button.collapsed::after,
.accordion-button:not(.collapsed)::after {
    filter: invert(50%);
}


/* footer */
.footer-headings {
    color: var(--footer-subheading-color);
    font-weight: 700;
    font-size: 1rem;
}

.footer-links {
    color: var(--content-color);
    font-weight: 500;
    font-size: 1rem;
}

/* copywrite-section */
.copywrite-container {
    color: #A7ADC5;
    font-weight: 400;
    font-size: 0.875rem;
}

.copywrite-container a {
    color: #A7ADC5;
    font-weight: 500;
    font-size: 0.875rem;
}







.typed-cursor {
    opacity: 1;
    animation: typedCursorBlink 0.7s infinite;
}

@keyframes typedCursorBlink {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}


/* Responsive Styling */
@media (max-width: 1024px) {
    .contact-heading {
        font-size: 1rem;
        font-weight: 600;
    }

    .contact-details {
        font-size: 0.9375rem;
        font-weight: 400;
    }

    .find {
        font-size: 1.25rem;
        font-weight: 600;
    }

    .contact-form {
        font-size: 1rem;
        font-weight: 400;
    }

    .contact-touch {
        font-size: 1.75rem;
        font-weight: 600;
    }

    .contact-text {
        font-size: 0.9375rem;
        font-weight: 400;
    }


    .card-text {
        margin-top: 80px;
        font-size: 1.125rem;
        font-weight: 500;
    }

    .student-avatar {
        margin-top: 60px;
    }

    .student-name {
        font-size: 1rem;
        font-weight: 600;
    }

    .designation {
        font-size: 0.9375rem;
        font-weight: 400;
    }

    .learning-content {
        max-width: 90%;
    }

    .custom-card {
        max-width: 100%;
    }

    .learning-heading {
        font-size: 1rem;
        font-weight: 600;
    }

    .learning-text {
        font-size: 0.875rem;
        font-weight: 400;
    }

    .feature-content {
        font-size: 1.25rem;
        /* 20px in rem */
        font-weight: 400;
        /* Normal for tablets */
    }

    .feature-title {
        font-size: 1.25rem;
        /* 20px in rem */
        font-weight: 700;
        /* Bold for tablets */
    }

    .primary-heading {
        font-size: 2.5rem;
        max-width: 60%;
    }

    .section-levl-subheading {
        font-size: 0.95rem;
        max-width: 75%;
        margin: 25px auto;
    }

    .section-level-heading {
        font-size: 2rem;
    }


    .footer-headings {
        font-size: 0.9375rem;
        font-weight: 600;
    }

    .footer-links {
        font-size: 0.9375rem;
        font-weight: 500;
    }

    .copywrite-container {
        font-size: 0.8125rem;
        font-weight: 400;
    }

    .copywrite-container a {
        font-size: 0.8125rem;
        font-weight: 500;
    }

    .faq-title {
        font-size: 2rem;
        font-weight: 600;
    }

    .faq-item {
        font-size: 1.125rem;
        font-weight: 600;
    }

    .learning-content {
        max-width: 70%;
    }

    .custom-card {
        max-width: 100%;
    }

    .learning-heading {
        font-size: 1.125rem;
        font-weight: 600;
    }

    .learning-text {
        font-size: 0.9375rem;
        font-weight: 400;
    }




}



@media (max-width: 767px) {

    span.bar {
        width: 31px;
        height: 2px;
        display: block;
        background: #edeef4;
        margin-bottom: 5px;
    }

    nav .container-fluid {
        justify-content: space-between !important;
        align-items: end !important;
    }

    .navbar-brand {
        flex-grow: 1;
    }

    div#navbarScroll {
        margin-top: 18px;
    }

    a.nav-link.nav-link-text {
        font-size: 20px;
    }

    .hero-section {
        margin-top: 0%;
    }

    .hero-text {
        max-width: 75%;
        font-size: 18px;
    }

    #features {
        padding-right: 0 !important;
        padding-left: 0 !important;
    }

    .each-capability-block {
        padding-left: 0;
        margin-top: 30px;
        margin-bottom: 34px;
    }

    .row.justify-content-center.g-3.review-slider {
        max-width: 89%;
        margin: auto;
    }


    .card-text {
        margin-top: 60px;
        font-size: 1rem;
        font-weight: 500;
    }

    .student-avatar {
        margin-top: 40px;
    }

    .student-name {
        font-size: 0.9375rem;
        font-weight: 600;
    }

    .designation {
        font-size: 0.875rem;
        font-weight: 400;
    }

    .feature-content {
        font-size: 1.125rem;
        /* 18px in rem */
        font-weight: 400;
        text-align: center !important;
        max-width: 100%;
        /* Normal for small tablets */
    }

    .feature-title {
        font-size: 1.125rem;
        /* 18px in rem */
        text-align: center !important;
        font-weight: 700;
        /* Bold for small tablets */
    }

    .section-levl-subheading {
        font-size: 0.9rem;
        max-width: 85%;
        margin: 20px auto;
    }

    .section-level-heading {
        font-size: 1.8rem;
    }

    .primary-heading {
        font-size: 2.2rem;
        max-width: 70%;
    }

    .contact-card {
        flex-direction: column;
        text-align: center;
    }



    .contact-heading {
        font-size: 0.9375rem;
        font-weight: 600;
    }

    .contact-details {
        font-size: 0.875rem;
        font-weight: 400;
    }

    .find {
        font-size: 1.125rem;
        font-weight: 600;
        margin-left: 0;
    }

    .contact-form {
        font-size: 0.9375rem;
        font-weight: 400;
    }

    .contact-touch {
        font-size: 1.5rem;
        font-weight: 600;
    }

    .contact-text {
        font-size: 0.875rem;
        font-weight: 400;
    }


    .footer-headings {
        font-size: 0.875rem;
        font-weight: 600;
    }

    .footer-links {
        font-size: 0.875rem;
        font-weight: 500;
    }

    .copywrite-container {
        font-size: 0.75rem;
        font-weight: 400;
    }

    .copywrite-container a {
        font-size: 0.75rem;
        font-weight: 500;
    }


    .faq-title {
        font-size: 1.75rem;
        font-weight: 600;
    }

    .faq-item {
        font-size: 1rem;
        font-weight: 600;
    }

    .faq-subtext,
    .faq-answer {
        font-size: 0.875rem;
        font-weight: 400;
    }


}

@media (max-width: 576px) {
    .feature-content {
        font-size: 1rem;
        font-weight: 400;
        text-align: center !important;
        max-width: 100%;
    }

    #features .mt-5 {
        margin-top: 0 !important;
    }



    .find-us-inner-container {
        display: flex;
        flex-wrap: wrap;
        gap: 3px 21px;
    }

    .find-us-inner-container {
        display: flex;
        flex-wrap: wrap;
        gap: 3px 21px;
    }

    .contact-card:nth-child(1),
    .contact-card:nth-child(2) {
        flex-grow: 1;
    }

    .feature-grid .row {
        padding-right: 4%;
    }

    .section-levl-subheading {
        font-size: 16px;
        max-width: 81%;
        margin: 15px auto 32px;
    }

    .section-level-heading {
        font-size: 1.6rem;
        max-width: 87%;
        margin: auto;
        line-height: 158%;
    }

    .primary-heading {
        font-size: 22px;
        max-width: 58%;
        line-height: 37px;

    }

    .feature-title {
        font-size: 1rem;
        /* 16px in rem */
        font-weight: 700;
        text-align: center !important;
        /* Bold for mobile */
    }

    .last-card {
        margin-top: 20px;
    }


    .card-text {
        margin-top: 40px;
        font-size: 0.9375rem;
        font-weight: 500;
    }

    .student-avatar {
        margin-top: 30px;
    }

    .student-name {
        font-size: 0.875rem;
        font-weight: 600;
    }

    .designation {
        font-size: 0.8125rem;
        font-weight: 400;
    }


    .contact-heading {
        font-size: 0.875rem;
        font-weight: 600;
    }

    .contact-details {
        font-size: 0.8125rem;
        font-weight: 400;
    }

    .find {
        font-size: 1.6rem;
        font-weight: 600;
    }

    .contact-form {
        font-size: 0.875rem;
        font-weight: 400;
    }

    .contact-touch {
        font-size: 1.25rem;
        font-weight: 600;
    }

    .contact-text {
        font-size: 0.8125rem;
        font-weight: 400;
    }


    .learning-content {
        max-width: 100%;
    }

    .custom-card {
        max-width: 100%;
    }

    .learning-heading {
        font-size: 0.9375rem;
        font-weight: 600;
    }

    .learning-text {
        font-size: 0.8125rem;
        font-weight: 400;
    }

    .explore {
        font-size: 0.875rem;
    }

    .faq-title {
        font-size: 1.5rem;
        font-weight: 600;
    }

    .faq-item {
        font-size: 0.875rem;
        font-weight: 600;
    }

    .faq-subtext,
    .faq-answer {
        font-size: 0.75rem;
        font-weight: 400;
    }


    .footer-headings {
        font-size: 0.8125rem;
        font-weight: 600;
    }

    .footer-links {
        font-size: 0.8125rem;
        font-weight: 500;
    }

    .copywrite-container {
        font-size: 0.6875rem;
        font-weight: 400;
    }

    .copywrite-container a {
        font-size: 0.6875rem;
        font-weight: 500;
    }


}

@media only screen and (max-width: 510px) {
    .hero-section {
        max-width: 95%;
        padding-top: 24px !important;
        margin: auto;
    }

    .header-section {
        padding: 20px !important;
    }
}

@media only screen and (max-width: 470px) {

    .navbar-brand img {
        width: 130px;
    }

    .action-button {
        padding: 5px 10px !important;
    }

    .primary-heading {
        font-size: 22px;
        max-width: 73%;
        line-height: 37px;
    }

    .navbar {
        padding: 12px 0 !important;
    }

    span.bar {
        width: 30px;
        height: 2px;
    }

}